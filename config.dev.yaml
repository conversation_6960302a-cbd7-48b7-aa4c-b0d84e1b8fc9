# 开发环境配置文件
# 使用方法: ./gamelog.exe -env=dev
# 此文件中的配置会覆盖 config.yaml 中的对应配置
# 只需要配置需要在开发环境中修改的部分，其他配置会继承自 config.yaml

# 日志配置 - 开发环境启用调试日志
logging:
  # 全局日志级别改为debug
  level: debug
  
  # 模块化日志配置
  modules:
    # 系统模块启用调试日志
    system:
      level: debug
      
    # Gin HTTP访问日志输出到控制台，启用颜色
    gin:
      level: debug
      output: stdout
      gin_format:
        template: "{method} {path} -> {status} in {latency}"
        enable_color: true
        
    # 数据库日志也输出到控制台便于调试
    database:
      level: debug
      output: stdout

# BaseNet API配置 - 开发环境配置
basenet:
  # 游戏配置 - 开发环境便于测试
  game_config:
    max_level: 10  # 降低最大等级便于测试
    daily_reward: true
    maintenance: false
    force_update: false
