package main

import (
	"fmt"
	"log"

	"iaa-gamelog/internal/config"
	"iaa-gamelog/internal/database"
	"iaa-gamelog/internal/logger"
	"iaa-gamelog/internal/router"
)

var (
	db        database.DatabaseInterface
	appConfig *config.Config
)

// initDB 初始化数据库
func initDB(config *config.Config) {
	dbLogger := logger.DatabaseLogger()

	var err error
	switch config.Database.Type {
	case "mongodb":
		db, err = database.NewMongoAdapter(
			config.Database.MongoDB.URI,
			config.Database.MongoDB.Database,
			config.Database.MongoDB.Collection,
		)
		if err != nil {
			dbLogger.Error("MongoDB连接失败: %v", err)
			log.Fatal("MongoDB连接失败:", err)
		}
		dbLogger.Info("MongoDB数据库初始化成功")
	case "sqlserver":
		db, err = database.NewSQLServerAdapter(
			config.Database.SQLServer.Server,
			config.Database.SQLServer.Port,
			config.Database.SQLServer.Database,
			config.Database.SQLServer.Username,
			config.Database.SQLServer.Password,
			config.Database.SQLServer.Instance,
			config.Database.SQLServer.Encrypt,
		)
		if err != nil {
			dbLogger.Error("SQL Server连接失败: %v", err)
			log.Fatal("SQL Server连接失败:", err)
		}
		dbLogger.Info("SQL Server数据库初始化成功")
	default:
		dbLogger.Error("不支持的数据库类型: %s", config.Database.Type)
		log.Fatal("不支持的数据库类型:", config.Database.Type)
	}
}

func main() {
	// 加载配置文件
	var err error
	appConfig, err = config.LoadConfig("")
	if err != nil {
		log.Fatal("加载配置失败:", err)
	}

	// 初始化模块化日志系统
	if err := logger.InitLoggerManager(&appConfig.Logging); err != nil {
		log.Fatal("日志系统初始化失败:", err)
	}

	// 获取系统日志记录器
	sysLogger := logger.SystemLogger()

	// 初始化数据库
	initDB(appConfig)
	defer db.Close()

	// 设置路由
	r := router.SetupRouter(appConfig, db)

	// 服务器地址
	serverAddr := fmt.Sprintf("%s:%d", appConfig.Server.Host, appConfig.Server.Port)
	sysLogger.Info("服务器启动在 %s", serverAddr)

	// 打印路由信息
	router.LogRoutes()

	// 启动服务器
	if err := r.Run(serverAddr); err != nil {
		sysLogger.Error("服务器启动失败: %v", err)
		log.Fatal("服务器启动失败:", err)
	}
}
