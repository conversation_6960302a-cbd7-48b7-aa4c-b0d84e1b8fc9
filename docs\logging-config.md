# 日志配置说明

## 概述

IAA GameLog 支持模块化的日志配置，可以为不同模块（如系统、Gin、数据库、API等）设置独立的日志级别、输出位置和格式。

## 配置文件结构

```yaml
logging:
  # 全局默认配置
  level: info                    # 日志级别
  format: text                   # 日志格式
  output: stdout                 # 输出位置

  # 模块化配置
  modules:
    system:                      # 系统模块
      level: info
      format: text
      output: stdout

    gin:                         # Gin HTTP访问日志
      level: info
      format: text
      output: logs/api.log
      gin_format:                # Gin专用格式配置
        template: "[{time}] {method} {path} - {status} ({latency}) from {ip}"
        time_format: "2006-01-02 15:04:05"
        enable_color: false

    database:                    # 数据库模块
      level: warn
      format: text
      output: logs/database.log

    api:                         # API业务逻辑
      level: info
      format: text
      output: logs/business.log
```

## 模块化日志配置

### 支持的模块

| 模块名 | 说明 | 默认输出 | 推荐级别 |
|--------|------|----------|----------|
| `system` | 系统启动、配置、路由等 | stdout | info |
| `gin` | HTTP访问日志 | logs/api.log | info |
| `database` | 数据库连接、查询等 | logs/database.log | warn |
| `api` | API业务逻辑 | logs/business.log | info |

### 配置继承规则

1. **模块配置优先**：如果模块有专门配置，使用模块配置
2. **字段级继承**：模块配置的空字段会继承全局配置
3. **全局兜底**：没有模块配置时使用全局配置

### 使用示例

```go
// 在代码中使用模块化日志
import "iaa-gamelog/internal/logger"

// 获取系统日志记录器
sysLogger := logger.SystemLogger()
sysLogger.Info("系统启动完成")

// 获取数据库日志记录器
dbLogger := logger.DatabaseLogger()
dbLogger.Warn("数据库连接超时")

// 获取API日志记录器
apiLogger := logger.APILogger()
apiLogger.Error("API调用失败: %v", err)
```

## 日志级别 (level)

| 级别 | 说明 | Gin模式 | HTTP访问日志 |
|------|------|---------|-------------|
| `debug` | 显示所有调试信息 | Debug模式 | 记录所有请求 |
| `info` | 显示一般信息 | Release模式 | 记录所有请求 |
| `warn` | 只显示警告和错误 | Release模式 | 只记录4xx/5xx状态码 |
| `error` | 只显示错误 | Release模式 | 禁用HTTP访问日志 |

## 输出位置 (output)

- `stdout` - 标准输出（控制台）
- `stderr` - 错误输出
- `文件路径` - 输出到指定文件，如 `logs/app.log`

## Gin HTTP访问日志格式

### 支持的变量

| 变量 | 说明 | 示例 |
|------|------|------|
| `{time}` | 请求时间 | `2025-08-04 15:30:45` |
| `{method}` | HTTP方法 | `GET`, `POST` |
| `{path}` | 请求路径（含查询参数） | `/health?check=true` |
| `{status}` | HTTP状态码 | `200`, `404` |
| `{latency}` | 响应时间 | `1.2ms`, `500µs` |
| `{ip}` | 客户端IP | `127.0.0.1` |
| `{user_agent}` | 用户代理 | `Mozilla/5.0...` |
| `{error}` | 错误信息 | 错误详情 |

### 预设模板

#### 1. 详细格式（默认）
```yaml
template: "[{time}] {method} {path} - {status} ({latency}) from {ip}"
```
输出示例：
```
[INFO] [2025-08-04 15:30:45] GET /health - 200 (1.2ms) from 127.0.0.1
```

#### 2. 简洁格式
```yaml
template: "{method} {path} - {status} ({latency})"
```
输出示例：
```
[INFO] GET /health - 200 (1.2ms)
```

#### 3. 开发格式
```yaml
template: "{method} {path} -> {status} in {latency}"
```
输出示例：
```
[INFO] GET /health -> 200 in 1.2ms
```

#### 4. 完整格式
```yaml
template: "[{time}] {ip} \"{method} {path}\" {status} {latency} \"{user_agent}\""
```
输出示例：
```
[INFO] [2025-08-04 15:30:45] 127.0.0.1 "GET /health" 200 1.2ms "Mozilla/5.0..."
```

### 时间格式 (time_format)

使用 Go 的时间格式规范：
- `2006-01-02 15:04:05` - 标准格式
- `2006/01/02 - 15:04:05` - 斜杠分隔
- `15:04:05` - 只显示时间
- `2006-01-02T15:04:05Z07:00` - RFC3339格式

### 颜色支持 (enable_color)

- `true` - 启用ANSI颜色代码（适合控制台输出）
- `false` - 禁用颜色（适合文件输出）

颜色方案：
- **状态码**：200-299绿色，300-399白色，400-499黄色，500+红色
- **HTTP方法**：GET蓝色，POST青色，PUT黄色，DELETE红色等

## 实际效果示例

### 控制台输出（系统日志）
```
[SYSTEM] 2025/08/04 15:52:49 [INFO] 模块日志初始化完成 - Level: info, Format: text, Output: stdout
[SYSTEM] 2025/08/04 15:52:49 [INFO] CORS中间件已启用
[SYSTEM] 2025/08/04 15:52:49 [INFO] 服务器启动在 0.0.0.0:3001
[DATABASE] 2025/08/04 15:52:49 [INFO] SQL Server数据库初始化成功
```

### API日志文件（logs/api.log）
```
[INFO] [2025-08-04 15:53:11] GET /health - 200 (0s) from ::1
[INFO] [2025-08-04 15:53:52] GET /common/config/info?app_name=test&version=1.0 - 200 (525.3μs) from ::1
```

### 数据库日志文件（logs/database.log）
```
[DATABASE] 2025/08/04 15:55:23 [WARN] 数据库连接池达到上限
[DATABASE] 2025/08/04 15:55:45 [ERROR] 查询超时: SELECT * FROM game_data WHERE uuid = ?
```

## 配置示例

### 开发环境配置
```yaml
logging:
  level: debug
  format: text
  output: stdout

  modules:
    system:
      level: debug
      output: stdout

    gin:
      level: debug
      output: stdout
      gin_format:
        template: "{method} {path} -> {status} in {latency}"
        time_format: "15:04:05"
        enable_color: true

    database:
      level: debug
      output: stdout

    api:
      level: debug
      output: stdout
```

### 生产环境配置
```yaml
logging:
  level: warn
  format: text
  output: logs/system.log

  modules:
    system:
      level: info
      output: logs/system.log

    gin:
      level: info
      output: logs/access.log
      gin_format:
        template: "[{time}] {ip} \"{method} {path}\" {status} {latency}"
        time_format: "2006-01-02T15:04:05Z07:00"
        enable_color: false

    database:
      level: error
      output: logs/database.log

    api:
      level: warn
      output: logs/business.log
```

### 调试环境配置
```yaml
logging:
  level: debug
  format: text
  output: stdout

  modules:
    gin:
      level: debug
      output: logs/debug_api.log
      gin_format:
        template: "[{time}] {method} {path} - {status} ({latency}) from {ip} - {user_agent}"
        time_format: "2006-01-02 15:04:05"
        enable_color: false
```

## 日志轮转功能

从 v1.1 开始，系统支持自动日志轮转功能，基于 [lumberjack](https://github.com/natefinch/lumberjack) 库实现。

### 轮转配置

在模块配置中添加 `rotation` 配置项：

```yaml
logging:
  modules:
    gin:
      level: info
      output: logs/api.log
      rotation:
        max_size: 100      # 单个日志文件最大大小（MB）
        max_age: 30        # 日志文件保留天数
        max_backups: 10    # 保留的旧日志文件数量
        compress: true     # 是否压缩旧日志文件
```

### 轮转参数说明

- **max_size**: 单个日志文件的最大大小（MB），超过此大小会自动轮转
- **max_age**: 日志文件保留天数，超过此天数的文件会被自动删除
- **max_backups**: 保留的旧日志文件数量，0表示保留所有文件
- **compress**: 是否压缩旧日志文件为 .gz 格式

### 轮转文件命名

轮转后的文件按以下格式命名：
```
原文件名-时间戳.扩展名[.gz]
```

例如：
- `api.log` → `api-2025-08-04T15-30-00.000.log`
- 压缩后：`api-2025-08-04T15-30-00.000.log.gz`

## 注意事项

1. **文件输出**：确保日志目录存在，程序会自动创建日志文件
2. **颜色输出**：文件输出时建议禁用颜色，避免ANSI代码污染日志文件
3. **性能考虑**：debug级别会产生大量日志，生产环境建议使用warn或error级别
4. **日志轮转**：支持自动轮转，无需外部工具
5. **实时生效**：修改配置后需要重启服务才能生效
6. **轮转兼容性**：如果不配置 `rotation`，将使用传统的文件追加方式

## 故障排除

### 问题：日志文件无法创建
**解决**：检查目录权限，确保程序有写入权限

### 问题：颜色代码显示异常
**解决**：在不支持ANSI的终端中设置 `enable_color: false`

### 问题：日志格式不生效
**解决**：检查YAML语法，确保缩进正确，重启服务
