package config

import (
	"fmt"
	"io"
	"log"
	"os"
	"reflect"
	"strings"

	"gopkg.in/yaml.v3"
)

// Config 应用程序配置
type Config struct {
	Database DatabaseConfig `yaml:"database"`
	Server   ServerConfig   `yaml:"server"`
	Logging  LoggingConfig  `yaml:"logging"`
	Security SecurityConfig `yaml:"security"`
	BaseNet  BaseNetConfig  `yaml:"basenet"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Type      string          `yaml:"type"`
	MongoDB   MongoDBConfig   `yaml:"mongodb"`
	SQLServer SQLServerConfig `yaml:"sqlserver"`
}

// MongoDBConfig MongoDB配置
type MongoDBConfig struct {
	URI        string `yaml:"uri"`
	Database   string `yaml:"database"`
	Collection string `yaml:"collection"`
}

// SQLServerConfig SQL Server配置
type SQLServerConfig struct {
	Server   string `yaml:"server"`   // 服务器地址
	Port     int    `yaml:"port"`     // 端口号，默认1433
	Database string `yaml:"database"` // 数据库名
	Username string `yaml:"username"` // 用户名
	Password string `yaml:"password"` // 密码
	Instance string `yaml:"instance"` // 实例名（可选）
	Encrypt  bool   `yaml:"encrypt"`  // 是否启用加密，默认true
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port int        `yaml:"port"`
	Host string     `yaml:"host"`
	CORS CORSConfig `yaml:"cors"`
}

// CORSConfig CORS配置
type CORSConfig struct {
	Enabled        bool     `yaml:"enabled"`
	AllowedOrigins []string `yaml:"allowed_origins"`
	AllowedMethods []string `yaml:"allowed_methods"`
	AllowedHeaders []string `yaml:"allowed_headers"`
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	// 全局默认配置
	Level  string `yaml:"level"`
	Format string `yaml:"format"`
	Output string `yaml:"output"`

	// 模块化配置
	Modules map[string]ModuleLogConfig `yaml:"modules"`
}

// ModuleLogConfig 模块日志配置
type ModuleLogConfig struct {
	Level     string             `yaml:"level"`
	Format    string             `yaml:"format"`
	Output    string             `yaml:"output"`
	GinFormat *GinFormatConfig   `yaml:"gin_format,omitempty"`
	Rotation  *LogRotationConfig `yaml:"rotation,omitempty"` // 日志轮转配置
}

// GinFormatConfig Gin日志格式配置
type GinFormatConfig struct {
	Template    string `yaml:"template"`
	TimeFormat  string `yaml:"time_format"`
	EnableColor bool   `yaml:"enable_color"`
}

// LogRotationConfig 日志轮转配置
type LogRotationConfig struct {
	MaxSize    int  `yaml:"max_size"`    // 单个日志文件最大大小（MB）
	MaxAge     int  `yaml:"max_age"`     // 日志文件保留天数
	MaxBackups int  `yaml:"max_backups"` // 保留的旧日志文件数量
	Compress   bool `yaml:"compress"`    // 是否压缩旧日志文件
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	AppSecret string `yaml:"app_secret"`
}

// BaseNetConfig BaseNet API配置
type BaseNetConfig struct {
	Platforms  PlatformsConfig `yaml:"platforms"`
	GameConfig GameConfig      `yaml:"game_config"`
	AdsConfig  AdsConfig       `yaml:"ads_config"`
}

// PlatformsConfig 平台配置
type PlatformsConfig struct {
	Wechat PlatformConfig `yaml:"wechat"`
	Baidu  PlatformConfig `yaml:"baidu"`
	QQ     PlatformConfig `yaml:"qq"`
}

// PlatformConfig 单个平台配置
type PlatformConfig struct {
	AppID     string `yaml:"app_id"`
	AppSecret string `yaml:"app_secret"`
}

// GameConfig 游戏配置
type GameConfig struct {
	MaxLevel    int  `yaml:"max_level"`
	DailyReward bool `yaml:"daily_reward"`
	Maintenance bool `yaml:"maintenance"`
	ForceUpdate bool `yaml:"force_update"`
}

// AdsConfig 广告配置
type AdsConfig struct {
	Banner       AdTypeConfig `yaml:"banner"`
	Video        AdTypeConfig `yaml:"video"`
	Interstitial AdTypeConfig `yaml:"interstitial"`
}

// AdTypeConfig 广告类型配置
type AdTypeConfig struct {
	Enabled         bool `yaml:"enabled"`
	RefreshInterval int  `yaml:"refresh_interval,omitempty"`
	RewardAmount    int  `yaml:"reward_amount,omitempty"`
	Frequency       int  `yaml:"frequency,omitempty"`
}

// mergeConfigs 深度合并两个配置，override会覆盖base中的对应字段
func mergeConfigs(base, override *Config) *Config {
	if override == nil {
		return base
	}
	if base == nil {
		return override
	}

	// 创建base的副本
	result := *base

	// 合并数据库配置
	if override.Database.Type != "" {
		result.Database.Type = override.Database.Type
	}

	// 合并MongoDB配置
	if override.Database.MongoDB.URI != "" {
		result.Database.MongoDB.URI = override.Database.MongoDB.URI
	}
	if override.Database.MongoDB.Database != "" {
		result.Database.MongoDB.Database = override.Database.MongoDB.Database
	}
	if override.Database.MongoDB.Collection != "" {
		result.Database.MongoDB.Collection = override.Database.MongoDB.Collection
	}

	// 合并SQL Server配置
	if override.Database.SQLServer.Server != "" {
		result.Database.SQLServer.Server = override.Database.SQLServer.Server
	}
	if override.Database.SQLServer.Port != 0 {
		result.Database.SQLServer.Port = override.Database.SQLServer.Port
	}
	if override.Database.SQLServer.Database != "" {
		result.Database.SQLServer.Database = override.Database.SQLServer.Database
	}
	if override.Database.SQLServer.Username != "" {
		result.Database.SQLServer.Username = override.Database.SQLServer.Username
	}
	if override.Database.SQLServer.Password != "" {
		result.Database.SQLServer.Password = override.Database.SQLServer.Password
	}
	if override.Database.SQLServer.Instance != "" {
		result.Database.SQLServer.Instance = override.Database.SQLServer.Instance
	}
	// 对于bool类型，我们需要检查是否在override中被显式设置
	overrideValue := reflect.ValueOf(override.Database.SQLServer)
	baseValue := reflect.ValueOf(base.Database.SQLServer)
	if overrideValue.FieldByName("Encrypt").Bool() != baseValue.FieldByName("Encrypt").Bool() {
		result.Database.SQLServer.Encrypt = override.Database.SQLServer.Encrypt
	}

	// 合并服务器配置
	if override.Server.Port != 0 {
		result.Server.Port = override.Server.Port
	}
	if override.Server.Host != "" {
		result.Server.Host = override.Server.Host
	}

	// 合并CORS配置
	if override.Server.CORS.Enabled != base.Server.CORS.Enabled {
		result.Server.CORS.Enabled = override.Server.CORS.Enabled
	}
	if len(override.Server.CORS.AllowedOrigins) > 0 {
		result.Server.CORS.AllowedOrigins = override.Server.CORS.AllowedOrigins
	}
	if len(override.Server.CORS.AllowedMethods) > 0 {
		result.Server.CORS.AllowedMethods = override.Server.CORS.AllowedMethods
	}
	if len(override.Server.CORS.AllowedHeaders) > 0 {
		result.Server.CORS.AllowedHeaders = override.Server.CORS.AllowedHeaders
	}

	// 合并日志配置
	if override.Logging.Level != "" {
		result.Logging.Level = override.Logging.Level
	}
	if override.Logging.Format != "" {
		result.Logging.Format = override.Logging.Format
	}
	if override.Logging.Output != "" {
		result.Logging.Output = override.Logging.Output
	}

	// 合并模块日志配置
	if override.Logging.Modules != nil {
		if result.Logging.Modules == nil {
			result.Logging.Modules = make(map[string]ModuleLogConfig)
		}
		for moduleName, moduleConfig := range override.Logging.Modules {
			baseModuleConfig := result.Logging.Modules[moduleName]

			// 合并模块配置
			if moduleConfig.Level != "" {
				baseModuleConfig.Level = moduleConfig.Level
			}
			if moduleConfig.Format != "" {
				baseModuleConfig.Format = moduleConfig.Format
			}
			if moduleConfig.Output != "" {
				baseModuleConfig.Output = moduleConfig.Output
			}
			if moduleConfig.GinFormat != nil {
				baseModuleConfig.GinFormat = moduleConfig.GinFormat
			}
			if moduleConfig.Rotation != nil {
				baseModuleConfig.Rotation = moduleConfig.Rotation
			}

			result.Logging.Modules[moduleName] = baseModuleConfig
		}
	}

	// 合并安全配置
	if override.Security.AppSecret != "" {
		result.Security.AppSecret = override.Security.AppSecret
	}

	// 合并BaseNet配置
	mergeBaseNetConfig(&result.BaseNet, &override.BaseNet)

	return &result
}

// mergeBaseNetConfig 合并BaseNet配置
func mergeBaseNetConfig(base, override *BaseNetConfig) {
	// 合并平台配置
	mergePlatformConfig(&base.Platforms.Wechat, &override.Platforms.Wechat)
	mergePlatformConfig(&base.Platforms.Baidu, &override.Platforms.Baidu)
	mergePlatformConfig(&base.Platforms.QQ, &override.Platforms.QQ)

	// 合并游戏配置
	if override.GameConfig.MaxLevel != 0 {
		base.GameConfig.MaxLevel = override.GameConfig.MaxLevel
	}
	if override.GameConfig.DailyReward != base.GameConfig.DailyReward {
		base.GameConfig.DailyReward = override.GameConfig.DailyReward
	}
	if override.GameConfig.Maintenance != base.GameConfig.Maintenance {
		base.GameConfig.Maintenance = override.GameConfig.Maintenance
	}
	if override.GameConfig.ForceUpdate != base.GameConfig.ForceUpdate {
		base.GameConfig.ForceUpdate = override.GameConfig.ForceUpdate
	}

	// 合并广告配置
	mergeAdsConfig(&base.AdsConfig, &override.AdsConfig)
}

// mergePlatformConfig 合并平台配置
func mergePlatformConfig(base, override *PlatformConfig) {
	if override.AppID != "" {
		base.AppID = override.AppID
	}
	if override.AppSecret != "" {
		base.AppSecret = override.AppSecret
	}
}

// mergeAdsConfig 合并广告配置
func mergeAdsConfig(base, override *AdsConfig) {
	// 合并Banner配置
	if override.Banner.Enabled != base.Banner.Enabled {
		base.Banner.Enabled = override.Banner.Enabled
	}
	if override.Banner.RefreshInterval != 0 {
		base.Banner.RefreshInterval = override.Banner.RefreshInterval
	}

	// 合并Video配置
	if override.Video.Enabled != base.Video.Enabled {
		base.Video.Enabled = override.Video.Enabled
	}
	if override.Video.RewardAmount != 0 {
		base.Video.RewardAmount = override.Video.RewardAmount
	}

	// 合并Interstitial配置
	if override.Interstitial.Enabled != base.Interstitial.Enabled {
		base.Interstitial.Enabled = override.Interstitial.Enabled
	}
	if override.Interstitial.Frequency != 0 {
		base.Interstitial.Frequency = override.Interstitial.Frequency
	}
}

// LoadConfig 从YAML文件加载配置，支持开发环境配置覆盖
func LoadConfig(configPath string, env string) (*Config, error) {
	// 如果没有指定配置文件路径，使用默认路径
	if configPath == "" {
		configPath = "config.yaml"
	}

	// 检查基础配置文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("配置文件不存在: %s", configPath)
	}

	// 读取基础配置文件
	baseData, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 解析基础配置
	var baseConfig Config
	if err := yaml.Unmarshal(baseData, &baseConfig); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 检查是否需要加载开发环境配置
	devConfigPath := getDevConfigPath(configPath)
	var finalConfig *Config = &baseConfig

	// 检查是否指定了开发环境
	if shouldLoadDevConfig(env, devConfigPath) {
		log.Printf("检测到开发环境，加载开发配置: %s", devConfigPath)

		// 读取开发配置文件
		devData, err := os.ReadFile(devConfigPath)
		if err != nil {
			return nil, fmt.Errorf("读取开发配置文件失败: %v", err)
		}

		// 解析开发配置
		var devConfig Config
		if err := yaml.Unmarshal(devData, &devConfig); err != nil {
			return nil, fmt.Errorf("解析开发配置文件失败: %v", err)
		}

		// 合并配置（开发配置覆盖基础配置）
		finalConfig = mergeConfigs(&baseConfig, &devConfig)
		log.Printf("配置合并完成，开发配置已覆盖基础配置")
	}

	// 验证最终配置
	if err := validateConfig(finalConfig); err != nil {
		return nil, fmt.Errorf("配置验证失败: %v", err)
	}

	// 打印配置信息
	logConfigInfo(finalConfig)

	return finalConfig, nil
}

// getDevConfigPath 根据基础配置文件路径生成开发配置文件路径
func getDevConfigPath(basePath string) string {
	// 如果是默认的config.yaml，返回config.dev.yaml
	if basePath == "config.yaml" {
		return "config.dev.yaml"
	}

	// 对于其他路径，在扩展名前插入.dev
	// 例如: /path/to/config.yaml -> /path/to/config.dev.yaml
	if strings.HasSuffix(basePath, ".yaml") || strings.HasSuffix(basePath, ".yml") {
		ext := ".yaml"
		if strings.HasSuffix(basePath, ".yml") {
			ext = ".yml"
		}
		base := strings.TrimSuffix(basePath, ext)
		return base + ".dev" + ext
	}

	// 如果没有扩展名，直接添加.dev.yaml
	return basePath + ".dev.yaml"
}

// shouldLoadDevConfig 判断是否应该加载开发配置
func shouldLoadDevConfig(env string, devConfigPath string) bool {
	// 如果没有指定环境参数，不加载开发配置
	if env == "" {
		return false
	}

	// 检查环境参数是否为开发环境
	if env == "dev" || env == "development" || env == "debug" {
		// 检查开发配置文件是否存在
		if _, err := os.Stat(devConfigPath); err == nil {
			return true
		} else {
			log.Printf("警告: 指定了开发环境但配置文件不存在: %s", devConfigPath)
		}
	}

	return false
}

// validateConfig 验证配置
func validateConfig(config *Config) error {
	// 验证数据库类型
	if config.Database.Type != "mongodb" && config.Database.Type != "sqlserver" {
		return fmt.Errorf("不支持的数据库类型: %s", config.Database.Type)
	}

	// 验证MongoDB配置
	if config.Database.Type == "mongodb" {
		if config.Database.MongoDB.URI == "" {
			return fmt.Errorf("MongoDB URI不能为空")
		}
		if config.Database.MongoDB.Database == "" {
			return fmt.Errorf("MongoDB数据库名不能为空")
		}
		if config.Database.MongoDB.Collection == "" {
			return fmt.Errorf("MongoDB集合名不能为空")
		}
	}

	// 验证SQL Server配置
	if config.Database.Type == "sqlserver" {
		if config.Database.SQLServer.Server == "" {
			return fmt.Errorf("SQL Server服务器地址不能为空")
		}
		if config.Database.SQLServer.Database == "" {
			return fmt.Errorf("SQL Server数据库名不能为空")
		}
		if config.Database.SQLServer.Username == "" {
			return fmt.Errorf("SQL Server用户名不能为空")
		}
		if config.Database.SQLServer.Password == "" {
			return fmt.Errorf("SQL Server密码不能为空")
		}
		if config.Database.SQLServer.Port <= 0 || config.Database.SQLServer.Port > 65535 {
			return fmt.Errorf("无效的SQL Server端口: %d", config.Database.SQLServer.Port)
		}
	}

	// 验证服务器端口
	if config.Server.Port <= 0 || config.Server.Port > 65535 {
		return fmt.Errorf("无效的服务器端口: %d", config.Server.Port)
	}

	return nil
}

// logConfigInfo 打印配置信息
func logConfigInfo(config *Config) {
	log.Printf("数据库配置: Type=%s", config.Database.Type)
	switch config.Database.Type {
	case "mongodb":
		log.Printf("MongoDB URI: %s, DB: %s, Collection: %s",
			config.Database.MongoDB.URI,
			config.Database.MongoDB.Database,
			config.Database.MongoDB.Collection)
	case "sqlserver":
		log.Printf("SQL Server: %s:%d, DB: %s, User: %s, Encrypt: %t",
			config.Database.SQLServer.Server,
			config.Database.SQLServer.Port,
			config.Database.SQLServer.Database,
			config.Database.SQLServer.Username,
			config.Database.SQLServer.Encrypt)
	}
	log.Printf("服务器配置: Host=%s, Port=%d", config.Server.Host, config.Server.Port)
}

// GetDatabaseConfig 获取数据库配置（兼容旧接口）
func (c *Config) GetDatabaseConfig() *DatabaseConfig {
	return &c.Database
}

// InitLogging 根据配置初始化日志系统
func InitLogging(config *LoggingConfig) error {
	// 设置日志输出
	var output io.Writer
	switch strings.ToLower(config.Output) {
	case "stdout", "":
		output = os.Stdout
	case "stderr":
		output = os.Stderr
	default:
		// 如果是文件路径，创建或打开文件
		file, err := os.OpenFile(config.Output, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			return fmt.Errorf("无法打开日志文件 %s: %v", config.Output, err)
		}
		output = file
	}

	// 设置日志格式
	var flags int
	switch strings.ToLower(config.Format) {
	case "json":
		// JSON格式暂时使用标准格式，可以后续扩展
		flags = log.LstdFlags | log.Lshortfile
	case "text", "":
		flags = log.LstdFlags | log.Lshortfile
	default:
		flags = log.LstdFlags | log.Lshortfile
	}

	// 应用日志配置
	log.SetOutput(output)
	log.SetFlags(flags)

	// 根据日志级别设置前缀（简单实现）
	switch strings.ToLower(config.Level) {
	case "debug":
		log.SetPrefix("[DEBUG] ")
	case "info", "":
		log.SetPrefix("[INFO] ")
	case "warn", "warning":
		log.SetPrefix("[WARN] ")
	case "error":
		log.SetPrefix("[ERROR] ")
	default:
		log.SetPrefix("[INFO] ")
	}

	log.Printf("日志系统初始化完成 - Level: %s, Format: %s, Output: %s",
		config.Level, config.Format, config.Output)

	return nil
}

// GetModuleConfig 获取指定模块的日志配置，如果模块没有配置则返回全局配置
func (c *LoggingConfig) GetModuleConfig(moduleName string) ModuleLogConfig {
	if moduleConfig, exists := c.Modules[moduleName]; exists {
		// 如果模块配置的某些字段为空，使用全局配置填充
		if moduleConfig.Level == "" {
			moduleConfig.Level = c.Level
		}
		if moduleConfig.Format == "" {
			moduleConfig.Format = c.Format
		}
		if moduleConfig.Output == "" {
			moduleConfig.Output = c.Output
		}
		return moduleConfig
	}

	// 如果没有模块配置，返回全局配置
	return ModuleLogConfig{
		Level:  c.Level,
		Format: c.Format,
		Output: c.Output,
	}
}

// GetGinConfig 获取Gin模块的配置
func (c *LoggingConfig) GetGinConfig() (ModuleLogConfig, *GinFormatConfig) {
	ginConfig := c.GetModuleConfig("gin")

	// 如果Gin模块有专门的格式配置，使用它；否则使用默认配置
	if ginConfig.GinFormat != nil {
		return ginConfig, ginConfig.GinFormat
	}

	// 默认的Gin格式配置
	defaultGinFormat := &GinFormatConfig{
		Template:    "[{time}] {method} {path} - {status} ({latency}) from {ip}",
		TimeFormat:  "2006-01-02 15:04:05",
		EnableColor: false,
	}

	return ginConfig, defaultGinFormat
}

// InitModuleLogging 初始化指定模块的日志系统
func InitModuleLogging(config *LoggingConfig, moduleName string) error {
	moduleConfig := config.GetModuleConfig(moduleName)

	// 设置日志输出
	var output io.Writer
	switch strings.ToLower(moduleConfig.Output) {
	case "stdout", "":
		output = os.Stdout
	case "stderr":
		output = os.Stderr
	default:
		// 如果是文件路径，创建或打开文件
		file, err := os.OpenFile(moduleConfig.Output, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			return fmt.Errorf("无法打开模块 %s 的日志文件 %s: %v", moduleName, moduleConfig.Output, err)
		}
		output = file
	}

	// 设置日志格式
	var flags int
	switch strings.ToLower(moduleConfig.Format) {
	case "json":
		flags = log.LstdFlags | log.Lshortfile
	case "text", "":
		flags = log.LstdFlags | log.Lshortfile
	default:
		flags = log.LstdFlags | log.Lshortfile
	}

	// 应用日志配置
	log.SetOutput(output)
	log.SetFlags(flags)

	// 根据日志级别设置前缀
	switch strings.ToLower(moduleConfig.Level) {
	case "debug":
		log.SetPrefix(fmt.Sprintf("[DEBUG:%s] ", strings.ToUpper(moduleName)))
	case "info", "":
		log.SetPrefix(fmt.Sprintf("[INFO:%s] ", strings.ToUpper(moduleName)))
	case "warn", "warning":
		log.SetPrefix(fmt.Sprintf("[WARN:%s] ", strings.ToUpper(moduleName)))
	case "error":
		log.SetPrefix(fmt.Sprintf("[ERROR:%s] ", strings.ToUpper(moduleName)))
	default:
		log.SetPrefix(fmt.Sprintf("[INFO:%s] ", strings.ToUpper(moduleName)))
	}

	log.Printf("模块 %s 日志系统初始化完成 - Level: %s, Format: %s, Output: %s",
		moduleName, moduleConfig.Level, moduleConfig.Format, moduleConfig.Output)

	return nil
}

// GetMongoURI 获取MongoDB URI
func (c *Config) GetMongoURI() string {
	return c.Database.MongoDB.URI
}

// GetMongoDB 获取MongoDB数据库名
func (c *Config) GetMongoDB() string {
	return c.Database.MongoDB.Database
}

// GetMongoCollection 获取MongoDB集合名
func (c *Config) GetMongoCollection() string {
	return c.Database.MongoDB.Collection
}
