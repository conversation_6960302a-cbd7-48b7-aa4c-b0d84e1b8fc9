package logger

import (
	"fmt"
	"io"
	"log"
	"os"
	"strings"
	"sync"

	"iaa-gamelog/internal/config"

	"gopkg.in/natefinch/lumberjack.v2"
)

// ModuleLogger 模块日志记录器
type ModuleLogger struct {
	name   string
	logger *log.Logger
	level  string
}

// LoggerManager 日志管理器
type LoggerManager struct {
	config  *config.LoggingConfig
	loggers map[string]*ModuleLogger
	mutex   sync.RWMutex
}

var (
	globalManager *LoggerManager
	once          sync.Once
)

// InitLoggerManager 初始化全局日志管理器
func InitLoggerManager(config *config.LoggingConfig) error {
	var err error
	once.Do(func() {
		globalManager = &LoggerManager{
			config:  config,
			loggers: make(map[string]*ModuleLogger),
		}

		// 初始化系统日志
		_, err = globalManager.GetLogger("system")
	})
	return err
}

// GetLogger 获取指定模块的日志记录器
func GetLogger(moduleName string) *ModuleLogger {
	if globalManager == nil {
		// 如果管理器未初始化，创建一个默认的
		defaultConfig := &config.LoggingConfig{
			Level:  "info",
			Format: "text",
			Output: "stdout",
		}
		InitLoggerManager(defaultConfig)
	}

	logger, _ := globalManager.GetLogger(moduleName)
	return logger
}

// GetLogger 获取指定模块的日志记录器
func (lm *LoggerManager) GetLogger(moduleName string) (*ModuleLogger, error) {
	lm.mutex.RLock()
	if logger, exists := lm.loggers[moduleName]; exists {
		lm.mutex.RUnlock()
		return logger, nil
	}
	lm.mutex.RUnlock()

	// 创建新的模块日志记录器
	lm.mutex.Lock()
	defer lm.mutex.Unlock()

	// 双重检查
	if logger, exists := lm.loggers[moduleName]; exists {
		return logger, nil
	}

	// 获取模块配置
	moduleConfig := lm.config.GetModuleConfig(moduleName)

	// 设置输出
	var output io.Writer
	switch strings.ToLower(moduleConfig.Output) {
	case "stdout", "":
		output = os.Stdout
	case "stderr":
		output = os.Stderr
	default:
		// 检查是否配置了日志轮转
		if moduleConfig.Rotation != nil {
			// 使用 lumberjack 进行日志轮转
			output = &lumberjack.Logger{
				Filename:   moduleConfig.Output,
				MaxSize:    moduleConfig.Rotation.MaxSize,    // MB
				MaxAge:     moduleConfig.Rotation.MaxAge,     // days
				MaxBackups: moduleConfig.Rotation.MaxBackups, // files
				Compress:   moduleConfig.Rotation.Compress,   // gzip
			}
		} else {
			// 创建或打开文件（传统方式）
			file, err := os.OpenFile(moduleConfig.Output, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
			if err != nil {
				return nil, fmt.Errorf("无法打开模块 %s 的日志文件 %s: %v", moduleName, moduleConfig.Output, err)
			}
			output = file
		}
	}

	// 设置日志格式
	var flags int
	switch strings.ToLower(moduleConfig.Format) {
	case "json":
		flags = log.LstdFlags | log.Lshortfile
	case "text", "":
		flags = log.LstdFlags | log.Lshortfile
	default:
		flags = log.LstdFlags | log.Lshortfile
	}

	// 创建日志记录器
	logger := log.New(output, fmt.Sprintf("[%s] ", strings.ToUpper(moduleName)), flags)

	moduleLogger := &ModuleLogger{
		name:   moduleName,
		logger: logger,
		level:  strings.ToLower(moduleConfig.Level),
	}

	lm.loggers[moduleName] = moduleLogger

	moduleLogger.Info("模块日志初始化完成 - Level: %s, Format: %s, Output: %s",
		moduleConfig.Level, moduleConfig.Format, moduleConfig.Output)

	return moduleLogger, nil
}

// shouldLog 检查是否应该记录指定级别的日志
func (ml *ModuleLogger) shouldLog(level string) bool {
	levelPriority := map[string]int{
		"debug": 0,
		"info":  1,
		"warn":  2,
		"error": 3,
	}

	currentPriority, exists := levelPriority[ml.level]
	if !exists {
		currentPriority = 1 // 默认info级别
	}

	logPriority, exists := levelPriority[level]
	if !exists {
		logPriority = 1 // 默认info级别
	}

	return logPriority >= currentPriority
}

// Debug 记录调试日志
func (ml *ModuleLogger) Debug(format string, v ...interface{}) {
	if ml.shouldLog("debug") {
		ml.logger.Printf("[DEBUG] "+format, v...)
	}
}

// Info 记录信息日志
func (ml *ModuleLogger) Info(format string, v ...interface{}) {
	if ml.shouldLog("info") {
		ml.logger.Printf("[INFO] "+format, v...)
	}
}

// Warn 记录警告日志
func (ml *ModuleLogger) Warn(format string, v ...interface{}) {
	if ml.shouldLog("warn") {
		ml.logger.Printf("[WARN] "+format, v...)
	}
}

// Error 记录错误日志
func (ml *ModuleLogger) Error(format string, v ...interface{}) {
	if ml.shouldLog("error") {
		ml.logger.Printf("[ERROR] "+format, v...)
	}
}

// Print 直接打印（不带级别前缀）
func (ml *ModuleLogger) Print(v ...interface{}) {
	ml.logger.Print(v...)
}

// Printf 直接格式化打印（不带级别前缀）
func (ml *ModuleLogger) Printf(format string, v ...interface{}) {
	ml.logger.Printf(format, v...)
}

// Println 直接打印行（不带级别前缀）
func (ml *ModuleLogger) Println(v ...interface{}) {
	ml.logger.Println(v...)
}

// 便捷函数，用于快速获取常用模块的日志记录器

// SystemLogger 获取系统日志记录器
func SystemLogger() *ModuleLogger {
	return GetLogger("system")
}

// GinLogger 获取Gin日志记录器
func GinLogger() *ModuleLogger {
	return GetLogger("gin")
}

// DatabaseLogger 获取数据库日志记录器
func DatabaseLogger() *ModuleLogger {
	return GetLogger("database")
}
