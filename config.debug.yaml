# 开发环境配置文件
# 此文件中的配置会覆盖 config.yaml 中的对应配置
# 只需要配置需要在开发环境中修改的部分，其他配置会继承自 config.yaml

# 数据库配置 - 开发环境使用本地数据库
database:
  # 可以切换到MongoDB进行开发测试
  # type: mongodb
  
  # 本地SQL Server配置
  sqlserver:
    server: localhost
    port: 1433
    database: IAA_MiniGames_Dev
    username: sa
    password: dev_password
    encrypt: false

# 服务器配置 - 开发环境使用不同端口
server:
  port: 3000
  host: localhost

# 日志配置 - 开发环境启用调试日志
logging:
  # 全局日志级别改为debug
  level: debug
  
  # 模块化日志配置
  modules:
    # 系统模块启用调试日志
    system:
      level: debug
      
    # Gin HTTP访问日志输出到控制台，启用颜色
    gin:
      level: debug
      output: stdout
      gin_format:
        template: "{method} {path} -> {status} in {latency}"
        enable_color: true
        
    # 数据库日志也输出到控制台便于调试
    database:
      level: debug
      output: stdout

# 安全配置 - 开发环境使用简单的密钥
security:
  app_secret: "dev_secret_key_for_testing_only"

# BaseNet API配置 - 开发环境配置
basenet:
  # 平台配置 - 使用测试账号
  platforms:
    wechat:
      app_id: "dev_wechat_app_id"
      app_secret: "dev_wechat_app_secret"
    baidu:
      app_id: "dev_baidu_app_id"
      app_secret: "dev_baidu_app_secret"
    qq:
      app_id: "dev_qq_app_id"
      app_secret: "dev_qq_app_secret"

  # 游戏配置 - 开发环境便于测试
  game_config:
    max_level: 10  # 降低最大等级便于测试
    daily_reward: true
    maintenance: false
    force_update: false

  # 广告配置 - 开发环境禁用广告
  ads_config:
    banner:
      enabled: false
    video:
      enabled: false
      reward_amount: 100  # 增加奖励便于测试
    interstitial:
      enabled: false
